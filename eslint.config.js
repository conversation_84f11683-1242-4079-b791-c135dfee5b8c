import love from 'eslint-config-love'
import prettier from 'eslint-config-prettier'
import prettierPlugin from 'eslint-plugin-prettier'

export default [
  {
    ignores: ['node_modules/**', 'dist/**', 'coverage/**', '.env']
  },
  ...love,
  prettier,
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json'
      }
    },
    plugins: {
      prettier: prettierPlugin
    },
    rules: {
      'no-console': 'off',
      'prettier/prettier': 'error',
      '@typescript-eslint/dot-notation': 'off',
      '@typescript-eslint/strict-boolean-expressions': 'off',
      '@typescript-eslint/space-before-function-paren': 'off',
      '@typescript-eslint/restrict-template-expressions': 'off',
      '@typescript-eslint/triple-slash-reference': 'off',
      '@typescript-eslint/indent': 'off',
      '@typescript-eslint/no-misused-promises': 'off',
      '@typescript-eslint/no-namespace': 'off'
    }
  }
]
