{"name": "api-v2", "version": "1.0.0", "main": "./src/app.ts", "license": "MIT", "scripts": {"build": "rimraf ./dist && tsc", "dev": "nodemon ./src/app.ts", "start": "pnpm build && node ./dist/app.js", "format": "prettier --write ./src", "lint": "eslint --fix ./src", "seed": "ts-node ./src/seeds/seeder.ts"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "helmet": "^6.0.1", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "xss-clean": "^0.1.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/hpp": "^0.2.2", "@types/jsonwebtoken": "^9.0.1", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.57.1", "cross-env": "^7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^34.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.0.0", "mongoose-seed": "^0.6.0", "nodemon": "^2.0.22", "prettier": "^2.8.7", "rimraf": "^4.4.1", "ts-node": "^10.9.1", "typescript": "^5.0.3"}, "packageManager": "pnpm@8.0.0"}